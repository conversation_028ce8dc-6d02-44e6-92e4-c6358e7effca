# yaml-language-server: $schema=https://schema.zeabur.app/template.json
apiVersion: zeabur.com/v1
kind: Template
metadata:
    name: Gemini Balance
spec:
    description: A load balancing service for Google Gemini API with advanced features and monitoring
    icon: https://upload.wikimedia.org/wikipedia/commons/8/8f/Google-gemini-icon.svg
    variables:
        - key: PUBLIC_DOMAIN
          type: DOMAIN
          name: Domain
          description: What is the domain for your Gemini Balance service?
        - key: GEMINI_API_KEYS
          type: STRING
          name: Gemini API Keys (JSON Array)
          description: Your Gemini API keys in JSON array format, e.g., ["AIzaSyxxxxxxxxxxxxxxxxxxx","AIzaSyxxxxxxxxxxxxxxxxxxx"]
        - key: ALLOWED_TOKENS
          type: STRING
          name: Allowed Tokens (JSON Array)
          description: Allowed authentication tokens in JSON array format, e.g., ["sk-123456"]
        - key: AUTH_TOKEN
          type: STRING
          name: Auth Token
          description: Authentication token for accessing the API, e.g., sk-123456
    services:
        - name: mysql
          icon: https://raw.githubusercontent.com/zeabur/service-icons/main/marketplace/mysql.svg
          template: PREBUILT
          spec:
            source:
                image: mysql:8.0
            ports:
                - id: database
                  port: 3306
                  type: TCP
            volumes:
                - id: data
                  dir: /var/lib/mysql
            instructions:
                - type: TEXT
                  title: Command to connect to your MySQL
                  content: mysqlsh --sql --host=${PORT_FORWARDED_HOSTNAME} --port=${DATABASE_PORT_FORWARDED_PORT} --user=${MYSQL_USERNAME} --password=${MYSQL_PASSWORD} --schema=${MYSQL_DATABASE}
                - type: TEXT
                  title: MySQL username
                  content: ${MYSQL_USERNAME}
                  category: Credentials
                - type: PASSWORD
                  title: MySQL password
                  content: ${MYSQL_PASSWORD}
                  category: Credentials
                - type: TEXT
                  title: MySQL database
                  content: ${MYSQL_DATABASE}
                  category: Credentials
                - type: TEXT
                  title: MySQL host
                  content: ${PORT_FORWARDED_HOSTNAME}
                  category: Hostname & Port
                - type: TEXT
                  title: MySQL port
                  content: ${DATABASE_PORT_FORWARDED_PORT}
                  category: Hostname & Port
            env:
                MYSQL_DATABASE:
                    default: zeabur
                    expose: true
                MYSQL_HOST:
                    default: ${CONTAINER_HOSTNAME}
                    expose: true
                    readonly: true
                MYSQL_PASSWORD:
                    default: ${MYSQL_ROOT_PASSWORD}
                    expose: true
                    readonly: true
                MYSQL_PORT:
                    default: ${DATABASE_PORT}
                    expose: true
                    readonly: true
                MYSQL_ROOT_PASSWORD:
                    default: ${PASSWORD}
                MYSQL_USERNAME:
                    default: root
                    expose: true
                    readonly: true
            configs:
                - path: /etc/my.cnf
                  template: |
                    [mysqld]
                    default-authentication-plugin=mysql_native_password
                    host-cache-size=0
                    skip-name-resolve
                    datadir=/var/lib/mysql
                    socket=/var/run/mysqld/mysqld.sock
                    secure-file-priv=/var/lib/mysql-files
                    user=mysql
                    max_allowed_packet=10M
                    performance_schema=off

                    pid-file=/var/run/mysqld/mysqld.pid
                    [client]
                    socket=/var/run/mysqld/mysqld.sock

                    !includedir /etc/mysql/conf.d/
                  permission: null
                  envsubst: null
        - name: gemini-balance
          icon: https://upload.wikimedia.org/wikipedia/commons/8/8f/Google-gemini-icon.svg
          dependencies:
            - mysql
          template: PREBUILT
          spec:
            source:
                image: ghcr.io/snailyp/gemini-balance:latest
            ports:
                - id: web
                  port: 8000
                  type: HTTP
            env:
                # Required Database Configuration
                DATABASE_TYPE:
                    default: mysql
                MYSQL_HOST:
                    default: ${MYSQL_HOST}
                MYSQL_PORT:
                    default: "3306"
                MYSQL_USER:
                    default: ${MYSQL_USERNAME}
                MYSQL_PASSWORD:
                    default: ${MYSQL_PASSWORD}
                MYSQL_DATABASE:
                    default: ${MYSQL_DATABASE}

                # Required API Configuration
                API_KEYS:
                    default: ${GEMINI_API_KEYS}
                ALLOWED_TOKENS:
                    default: ${ALLOWED_TOKENS}
                AUTH_TOKEN:
                    default: ${AUTH_TOKEN}
          domainKey: PUBLIC_DOMAIN
