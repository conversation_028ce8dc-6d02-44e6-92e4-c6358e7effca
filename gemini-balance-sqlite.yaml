# yaml-language-server: $schema=https://schema.zeabur.app/template.json
apiVersion: zeabur.com/v1
kind: Template
metadata:
    name: Gemini Balance SQLite
spec:
    description: A lightweight load balancing service for Google Gemini API with SQLite database and advanced features
    icon: https://upload.wikimedia.org/wikipedia/commons/8/8f/Google-gemini-icon.svg
    variables:
        - key: PUBLIC_DOMAIN
          type: DOMAIN
          name: Domain
          description: What is the domain for your Gemini Balance service?

    services:
        - name: gemini-balance
          icon: https://upload.wikimedia.org/wikipedia/commons/8/8f/Google-gemini-icon.svg
          template: PREBUILT
          spec:
            source:
                image: ghcr.io/snailyp/gemini-balance:latest
            ports:
                - id: web
                  port: 8000
                  type: HTTP
            volumes:
                - id: sqlite-data
                  dir: /app/data
            env:
                # Database Configuration
                DATABASE_TYPE:
                    default: sqlite
                SQLITE_DATABASE:
                    default: default_db
                
                # API Configuration
                API_KEYS:
                    default: '["AIzaSyBDSz6oIQo1NFUPMCvuhY8sHUzsilJGRpI","AIzaSyCwylD2hLK3Tr0nH0dgWooc-D_kL7_5ZC4","AIzaSyBN7ry2fMbReYbVbkPLJRfN3SGjS4zJquM","AIzaSyAex1xYL7oyVjJ8KXlHiMiw4jhotP4WILw","AIzaSyBjKA4JTzbmWGAX2-xN3pFznJZrrDidgNY","AIzaSyBoQbpxCvCEfQqQ5b9XXEMM5bu9oOrEyKw"]'
                ALLOWED_TOKENS:
                    default: '["sk-gemini-proxy-token-123456"]'
                AUTH_TOKEN:
                    default: sk-gemini-proxy-token-123456
                
                # Timezone Configuration
                TZ:
                    default: Asia/Shanghai
          domainKey: PUBLIC_DOMAIN
