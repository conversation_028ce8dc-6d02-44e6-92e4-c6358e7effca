# yaml-language-server: $schema=https://schema.zeabur.app/template.json
apiVersion: zeabur.com/v1
kind: Template
metadata:
    name: Gemini Balance SQLite
spec:
    description: A lightweight load balancing service for Google Gemini API with SQLite database and advanced features
    icon: https://upload.wikimedia.org/wikipedia/commons/8/8f/Google-gemini-icon.svg
    variables:
        - key: PUBLIC_DOMAIN
          type: DOMAIN
          name: Domain
          description: What is the domain for your Gemini Balance service?
        - key: GEMINI_API_KEYS
          type: STRING
          name: Gemini API Keys (JSON Array)
          description: Your Gemini API keys in JSON array format, e.g., ["AIzaSyxxxxxxxxxxxxxxxxxxx","AIzaSyxxxxxxxxxxxxxxxxxxx"]
        - key: ALLOWED_TOKENS
          type: STRING
          name: Allowed Tokens (JSON Array)
          description: Allowed authentication tokens in JSON array format, e.g., ["sk-123456"]
        - key: AUTH_TOKEN
          type: STRING
          name: Auth Token (Optional)
          description: Optional super admin token with all permissions, e.g., sk-123456. If not set, uses first token from ALLOWED_TOKENS
    services:
        - name: gemini-balance
          icon: https://upload.wikimedia.org/wikipedia/commons/8/8f/Google-gemini-icon.svg
          template: PREBUILT
          spec:
            source:
                image: ghcr.io/snailyp/gemini-balance:latest
            ports:
                - id: web
                  port: 8000
                  type: HTTP
            volumes:
                - id: sqlite-data
                  dir: /app/data
            env:
                # Database Configuration
                DATABASE_TYPE:
                    default: sqlite
                SQLITE_DATABASE:
                    default: default_db
                
                # API Configuration
                API_KEYS:
                    default: ${GEMINI_API_KEYS}
                ALLOWED_TOKENS:
                    default: ${ALLOWED_TOKENS}
                AUTH_TOKEN:
                    default: ${AUTH_TOKEN}
                
                # Timezone Configuration
                TZ:
                    default: Asia/Shanghai
          domainKey: PUBLIC_DOMAIN
